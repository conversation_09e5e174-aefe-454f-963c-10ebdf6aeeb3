* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #212121;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background-color: #171717;
    border-right: 1px solid #2d2d2d;
    display: flex;
    flex-direction: column;
    position: relative;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #2d2d2d;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s;
}

.logo-container:hover {
    background-color: #2d2d2d;
}

.logo {
    color: #ffffff;
    flex-shrink: 0;
}

.logo-text {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.version {
    font-size: 12px;
    color: #9ca3af;
    background-color: #374151;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 4px;
}

.dropdown-icon {
    color: #9ca3af;
    margin-left: auto;
}

.sidebar-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.new-chat-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: none;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
    color: #ffffff;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    margin-bottom: 16px;
    font-family: inherit;
}

.new-chat-btn:hover {
    background-color: #2d2d2d;
    border-color: #404040;
}

.new-chat-btn:active {
    transform: scale(0.98);
}

.chat-history {
    /* 聊天历史样式 */
}

.sidebar-footer {
    padding: 16px;
    border-top: 1px solid #2d2d2d;
}

.user-menu {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-menu:hover {
    background-color: #2d2d2d;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #374151;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #212121;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
    position: relative;
}

.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #2d2d2d;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s;
}

.sidebar-toggle:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.share-btn {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s;
}

.share-btn:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.chat-messages {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.welcome-screen {
    text-align: center;
    max-width: 600px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 32px;
}

/* 输入区域样式 */
.input-container {
    padding: 24px;
    background-color: #212121;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background-color: #40414f;
    border-radius: 12px;
    padding: 12px 16px;
    gap: 12px;
    max-width: 768px;
    margin: 0 auto;
    border: 1px solid #565869;
    transition: border-color 0.2s;
}

.input-wrapper:focus-within {
    border-color: #10a37f;
}

.attach-btn,
.voice-btn,
.send-btn {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.attach-btn:active,
.voice-btn:active {
    transform: scale(0.95);
}

.attach-btn:hover,
.voice-btn:hover {
    background-color: #565869;
    color: #ffffff;
}

.send-btn {
    background-color: #19c37d;
    color: #ffffff;
    width: 32px;
    height: 32px;
}

.send-btn:hover {
    background-color: #0d8f6f;
}

.send-btn:disabled {
    background-color: #565869;
    color: #8e8ea0;
    cursor: not-allowed;
}

.message-input {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    outline: none;
    resize: none;
    min-height: 24px;
    max-height: 200px;
    line-height: 1.5;
    font-family: inherit;
}

.message-input::placeholder {
    color: #8e8ea0;
}

/* 底部文本样式 */
.footer-text {
    text-align: center;
    padding: 16px 24px;
    color: #6b7280;
    font-size: 12px;
    line-height: 1.4;
    max-width: 768px;
    margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -260px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        width: 100%;
    }

    .chat-container {
        max-width: 100%;
    }

    .input-container {
        padding: 16px;
    }

    .chat-messages {
        padding: 16px;
    }

    .welcome-title {
        font-size: 24px;
    }
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #525252;
}

/* 聊天消息样式 */
.message {
    margin-bottom: 24px;
    display: flex;
    gap: 16px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #10a37f;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

.message-content {
    flex: 1;
    line-height: 1.6;
    color: #ffffff;
}

.user-message .message-avatar {
    background-color: #6366f1;
}

.assistant-message .message-avatar {
    background-color: #10a37f;
}