* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #212121;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: relative;
}

/* 左侧边栏 */
.sidebar {
    width: 68px;
    background-color: #171717;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0;
    position: relative;
    z-index: 10;
}

/* 侧边栏头部 - ChatGPT Logo */
.sidebar-header {
    margin-bottom: 24px;
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 12px 8px;
    border-radius: 12px;
    transition: background-color 0.2s;
    position: relative;
}

.logo-container:hover {
    background-color: #2a2a2a;
}

.chatgpt-logo {
    color: #ffffff;
    width: 24px;
    height: 24px;
}

.logo-text {
    font-size: 11px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    line-height: 1;
}

.version {
    font-size: 9px;
    color: #8e8ea0;
    background-color: #2a2a2a;
    padding: 2px 4px;
    border-radius: 3px;
    line-height: 1;
}

.dropdown-arrow {
    color: #8e8ea0;
    width: 12px;
    height: 12px;
    position: absolute;
    top: 8px;
    right: 4px;
}

/* 侧边栏图标按钮 */
.sidebar-icons {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
}

.sidebar-icon-btn {
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    border-radius: 8px;
    color: #8e8ea0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.sidebar-icon-btn:hover {
    background-color: #2a2a2a;
    color: #ffffff;
}

.sidebar-icon-btn:active {
    transform: scale(0.95);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #212121;
    position: relative;
}

/* 顶部分享按钮 */
.top-bar {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 5;
}

.share-btn {
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    border-radius: 8px;
    color: #8e8ea0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.share-btn:hover {
    background-color: #2a2a2a;
    color: #ffffff;
}

/* 聊天区域 */
.chat-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.welcome-container {
    text-align: center;
    max-width: 600px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.2;
}

/* 输入区域 */
.input-section {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.input-container {
    display: flex;
    align-items: center;
    background-color: #40414f;
    border-radius: 24px;
    padding: 12px 16px;
    gap: 12px;
    max-width: 768px;
    width: 100%;
    border: 1px solid #565869;
    transition: border-color 0.2s;
}

.input-container:focus-within {
    border-color: #10a37f;
}

/* 输入区域按钮 */
.attach-btn,
.voice-btn,
.tools-btn {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    gap: 4px;
}

.attach-btn:hover,
.voice-btn:hover,
.tools-btn:hover {
    background-color: #565869;
    color: #ffffff;
}

.tools-text {
    font-size: 14px;
    font-weight: 400;
}

/* 输入框 */
.message-input {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    outline: none;
    resize: none;
    min-height: 24px;
    max-height: 200px;
    line-height: 1.5;
    font-family: inherit;
}

.message-input::placeholder {
    color: #8e8ea0;
}

/* 底部免责声明 */
.footer-disclaimer {
    text-align: center;
    color: #8e8ea0;
    font-size: 12px;
    line-height: 1.4;
    max-width: 768px;
    padding: 0 20px;
}

/* 左下角图标 */
.bottom-left-icon {
    position: absolute;
    bottom: 16px;
    left: 16px;
    z-index: 5;
}

.bottom-left-icon svg {
    color: #8e8ea0;
    cursor: pointer;
    transition: color 0.2s ease;
}

.bottom-left-icon svg:hover {
    color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }

    .logo-text,
    .version {
        display: none;
    }

    .dropdown-arrow {
        display: none;
    }

    .input-container {
        max-width: calc(100vw - 40px);
    }

    .welcome-title {
        font-size: 24px;
    }

    .footer-disclaimer {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 50px;
    }

    .sidebar-icon-btn {
        width: 36px;
        height: 36px;
    }

    .welcome-title {
        font-size: 20px;
    }

    .input-section {
        padding: 16px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #525252;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.welcome-container {
    animation: fadeIn 0.6s ease-out;
}

/* 焦点状态 */
.message-input:focus {
    outline: none;
}

/* 按钮活动状态 */
.sidebar-icon-btn:active,
.attach-btn:active,
.voice-btn:active,
.tools-btn:active,
.share-btn:active {
    transform: scale(0.95);
}