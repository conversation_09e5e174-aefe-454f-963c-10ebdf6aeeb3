document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatMessages = document.getElementById('chatMessages');
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const newChatBtn = document.querySelector('.new-chat-btn');
    const attachBtn = document.querySelector('.attach-btn');
    const voiceBtn = document.querySelector('.voice-btn');
    const shareBtn = document.querySelector('.share-btn');

    // 侧边栏切换功能
    sidebarToggle.addEventListener('click', () => {
        sidebar.classList.toggle('open');
    });

    // 新建聊天功能
    newChatBtn.addEventListener('click', () => {
        clearChat();
    });

    // 发送消息功能
    sendBtn.addEventListener('click', () => {
        sendMessage();
    });

    // 回车发送消息
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 输入框变化时更新发送按钮状态
    messageInput.addEventListener('input', () => {
        updateSendButton();
    });

    // 附件按钮功能
    attachBtn.addEventListener('click', () => {
        // 模拟文件选择
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*,text/*,.pdf,.doc,.docx';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                console.log('选择的文件:', file.name);
                // 这里可以添加文件上传逻辑
            }
        };
        input.click();
    });

    // 语音按钮功能
    voiceBtn.addEventListener('click', () => {
        // 模拟语音输入
        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.lang = 'zh-CN';
            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                messageInput.value = transcript;
                updateSendButton();
            };
            recognition.start();
        } else {
            alert('您的浏览器不支持语音识别功能');
        }
    });

    // 分享按钮功能
    shareBtn.addEventListener('click', () => {
        // 模拟分享功能
        if (navigator.share) {
            navigator.share({
                title: 'ChatGPT 对话',
                text: '查看我与ChatGPT的对话',
                url: window.location.href
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('链接已复制到剪贴板');
            });
        }
    });

    // 发送消息函数
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        // 清除欢迎屏幕
        const welcomeScreen = document.querySelector('.welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.remove();
        }

        // 添加用户消息
        addMessage(message, 'user');

        // 清空输入框
        messageInput.value = '';
        updateSendButton();

        // 模拟AI回复
        setTimeout(() => {
            const responses = [
                '我理解您的问题。让我来帮助您解决这个问题。',
                '这是一个很好的问题。根据我的理解...',
                '我可以为您提供以下建议...',
                '让我为您分析一下这个情况...',
                '基于您提供的信息，我认为...'
            ];
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            addMessage(randomResponse, 'assistant');
        }, 1000);
    }

    // 添加消息到聊天区域
    function addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? 'U' : 'AI';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        chatMessages.appendChild(messageDiv);

        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 清除聊天记录
    function clearChat() {
        chatMessages.innerHTML = `
            <div class="welcome-screen">
                <h1 class="welcome-title">有什么可以帮忙的?</h1>
            </div>
        `;
    }

    // 更新发送按钮状态
    function updateSendButton() {
        const hasText = messageInput.value.trim().length > 0;
        sendBtn.disabled = !hasText;
        sendBtn.style.opacity = hasText ? '1' : '0.5';
    }

    // 初始化发送按钮状态
    updateSendButton();

    // 点击外部关闭侧边栏（移动端）
    document.addEventListener('click', (e) => {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        }
    });

    // 窗口大小变化时处理侧边栏
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('open');
        }
    });
});