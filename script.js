document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    const messageInput = document.getElementById('messageInput');
    const attachBtn = document.querySelector('.attach-btn');
    const voiceBtn = document.querySelector('.voice-btn');
    const toolsBtn = document.querySelector('.tools-btn');
    const shareBtn = document.querySelector('.share-btn');
    const sidebarIconBtns = document.querySelectorAll('.sidebar-icon-btn');
    const logoContainer = document.querySelector('.logo-container');
    const bottomLeftIcon = document.querySelector('.bottom-left-icon');

    // Logo点击功能
    logoContainer.addEventListener('click', () => {
        console.log('ChatGPT logo clicked');
        // 可以添加版本选择或设置功能
    });

    // 侧边栏图标按钮功能
    sidebarIconBtns.forEach((btn, index) => {
        btn.addEventListener('click', () => {
            switch(index) {
                case 0: // 编辑图标
                    console.log('编辑功能');
                    break;
                case 1: // 搜索图标
                    console.log('搜索功能');
                    break;
                case 2: // 用户图标
                    console.log('用户设置');
                    break;
            }
        });
    });

    // 输入框功能
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    messageInput.addEventListener('input', () => {
        // 输入框内容变化时的处理
        const hasContent = messageInput.value.trim().length > 0;
        if (hasContent) {
            messageInput.style.color = '#ffffff';
        }
    });

    // 附件按钮功能
    attachBtn.addEventListener('click', () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*,text/*,.pdf,.doc,.docx';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                console.log('选择的文件:', file.name);
                // 这里可以添加文件上传逻辑
            }
        };
        input.click();
    });

    // 语音按钮功能
    voiceBtn.addEventListener('click', () => {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            recognition.lang = 'zh-CN';
            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                messageInput.value = transcript;
            };
            recognition.start();
        } else {
            alert('您的浏览器不支持语音识别功能');
        }
    });

    // 工具按钮功能
    toolsBtn.addEventListener('click', () => {
        console.log('工具功能');
        // 可以添加工具选择功能
    });

    // 分享按钮功能
    shareBtn.addEventListener('click', () => {
        if (navigator.share) {
            navigator.share({
                title: 'ChatGPT 对话',
                text: '查看我与ChatGPT的对话',
                url: window.location.href
            });
        } else {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('链接已复制到剪贴板');
            });
        }
    });

    // 左下角图标功能
    bottomLeftIcon.addEventListener('click', () => {
        console.log('底部图标点击');
        // 可以添加帮助或设置功能
    });

    // 发送消息函数
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        console.log('发送消息:', message);

        // 清空输入框
        messageInput.value = '';

        // 这里可以添加实际的消息发送逻辑
        // 比如调用ChatGPT API或显示消息
    }

    // 显示通知函数
    function showNotification(text) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2a2a2a;
            color: #ffffff;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        `;
        notification.textContent = text;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 添加一些交互效果
    messageInput.addEventListener('focus', () => {
        messageInput.parentElement.style.borderColor = '#10a37f';
    });

    messageInput.addEventListener('blur', () => {
        messageInput.parentElement.style.borderColor = '#565869';
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K 聚焦到输入框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            messageInput.focus();
        }
    });

    console.log('ChatGPT 界面已加载完成');
});