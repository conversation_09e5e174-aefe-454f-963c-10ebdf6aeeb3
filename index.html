<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2IDBDOC4yNjggMCAyIDcuMjY4IDIgMTVTOS4yNjggMzAgMTYgMzBTMzAgMjMuNzMyIDMwIDE2UzIzLjczMiAyIDE2IDJaIiBmaWxsPSIjNzQxQUE5Ii8+CjxwYXRoIGQ9Ik0xNiA4QzEyLjY4NiA4IDEwIDEwLjY4NiAxMCAxNFMxMi42ODYgMjAgMTYgMjBTMjIgMTcuMzE0IDIyIDE0UzE5LjMxNCA4IDE2IDhaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K">
</head>
<body>
    <div class="app">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <svg class="logo" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <path d="M8 12L11 15L16 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span class="logo-text">ChatGPT</span>
                    <span class="version">4o</span>
                    <svg class="dropdown-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="sidebar-content">
                <div class="sidebar-actions">
                    <button class="new-chat-btn">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8 1V15M1 8H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        新建聊天
                    </button>
                </div>

                <div class="chat-history">
                    <!-- 聊天历史会在这里动态添加 -->
                </div>
            </div>

            <div class="sidebar-footer">
                <div class="user-menu">
                    <div class="user-avatar">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <circle cx="10" cy="7" r="3" stroke="currentColor" stroke-width="2"/>
                            <path d="M4 18c0-4 3-6 6-6s6 2 6 6" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="chat-container">
                <div class="chat-header">
                    <button class="sidebar-toggle">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M3 6H17M3 12H17M3 18H17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>

                    <div class="share-btn">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M13 7L8 2L3 7M8 2V14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-screen">
                        <h1 class="welcome-title">有什么可以帮忙的?</h1>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <button class="attach-btn">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M10 3V17M3 10H17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </button>

                        <input type="text" class="message-input" placeholder="向ChatGPT提问题" id="messageInput">

                        <button class="voice-btn">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <rect x="8" y="3" width="4" height="8" rx="2" stroke="currentColor" stroke-width="1.5"/>
                                <path d="M5 9v1a5 5 0 0 0 10 0V9M10 15v3M7 18h6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </button>

                        <button class="send-btn" id="sendBtn">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M8 1L15 8L8 15M15 8H1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="footer-text">
                ChatGPT 也可能会出错。OpenAI 不会对Andrew David Ukraine 或工作场所工作实践或其他法律问题提供建议。
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>